// Copyright Epic Games, Inc. All Rights Reserved.

/*=============================================================================
	AndroidDeviceDetection.h: AndroidDeviceDetection module public header file.
=============================================================================*/

#pragma once


/* Boilerplate
 *****************************************************************************/

#include "Misc/MonolithicHeaderBoilerplate.h"
MONOLITHIC_HEADER_BOILERPLATE()

/* Dependencies
 *****************************************************************************/

#include "Core.h"
#include "Modules/ModuleManager.h"

/* Interfaces
 *****************************************************************************/

#include "Interfaces/IAndroidDeviceDetection.h"
#include "Interfaces/IAndroidDeviceDetectionModule.h"
