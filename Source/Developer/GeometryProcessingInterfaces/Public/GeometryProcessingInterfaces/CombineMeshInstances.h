// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Features/IModularFeature.h"
#include "DynamicMesh/DynamicMesh3.h"
#include "PhysicsEngine/AggregateGeom.h"

class UPrimitiveComponent;
class UStaticMesh;
class UMaterialInterface;
struct FMeshDescription;

/**
 * The CombineMeshInstances modular feature is used to provide a mechanism
 * for merging a set of instances of meshes (ie mesh + transform + materials + ...)
 * into a smaller set of meshes. Generally this involves creating simpler versions
 * of the instances and appending them into one or a small number of combined meshes.
 */
class IGeometryProcessing_CombineMeshInstances : public IModularFeature
{
public:
	virtual ~IGeometryProcessing_CombineMeshInstances() {}


	enum class EMeshDetailLevel
	{
		Base = 0,
		Standard = 1,
		Small = 2,
		Decorative = 3
	};


	enum class EApproximationType
	{
		NoConstraint = 0,
		AxisAlignedBox = 1 << 0,
		OrientedBox = 1 << 1,
		SweptHull = 1 << 2,
		ConvexHull = 1 << 3,
		SweptProjection = 1 << 4,
		All = 0xFFFF
	};

	// Set of versions of a single part mesh that can be used in the combined mesh, including source LODs, simplified versions, and approximated versions
	// Can be re-used across multiple CombineMeshInstances calls
	struct FSinglePartMeshSet
	{
		struct FErrorStats
		{
			double AverageError;
			double MaxError;
		};

		// Source LOD meshes
		TIndirectArray<UE::Geometry::FDynamicMesh3> Source;
		// Meshes generated by running simplification on a source LOD
		TIndirectArray<UE::Geometry::FDynamicMesh3> Simplified;
		// Error metrics for simplified meshes
		TArray<FErrorStats> SimplifiedMeshErrors;
		// Meshes generated by an approximation method (e.g. convex hull, sweep, etc), either from a source LOD or a simplified mesh
		TIndirectArray<UE::Geometry::FDynamicMesh3> Approximated;
		// Error metrics for simplified meshes
		TArray<FErrorStats> ApproximatedMeshErrors;

		// Note: Materials can vary per-instance so are not stored with the part mesh set
	};


	/**
	 * FMeshInstanceGroupData is data shared among one or more FBaseMeshInstances.
	 * For example all instances in an ISMC can share a single FMeshInstanceGroupData.
	 */
	struct FMeshInstanceGroupData
	{
		TArray<UMaterialInterface*> MaterialSet;
		
		bool bHasConstantOverrideVertexColor = false;
		FColor OverrideVertexColor;

		bool bPreserveUVs = false;
		bool bAllowMerging = true;		// if false, cannot merge the geometry from this mesh with adjacent meshes to reduce triangle count

		bool bAllowApproximation = true;			// if false, only Copied or Simplified LODs will be used for this Part. This flag will be combined w/ the Instance-level flag.

		// ApproximationConstraint can be used to control which types of Approximation are used for LODs of this Part.
		// This is a bitmask, any unset EApproximationType bits should be ignored by the CombineMeshInstances implementation
		// Note however that 0 (all bits unset) is 'NoConstraint', implementations are intended to treat this as 'Allow All Types'
		EApproximationType ApproximationConstraint = EApproximationType::NoConstraint;
	};


	/**
	 * FBaseMeshInstance is a base-struct for the various instance types below (FStaticMeshInstance, FMeshLODSetInstance)
	 */
	struct FBaseMeshInstance
	{
		EMeshDetailLevel DetailLevel = EMeshDetailLevel::Standard;
		TArray<FTransform3d> TransformSequence;		// set of transforms on this instance. Often just a single transform.
		int32 GroupDataIndex = -1;					// index into FSourceInstanceList::InstanceGroupDatas

		bool bAllowApproximation = true;			// if false, only Copied or Simplified LODs will be used for this part Instance. Will be combined w/ the GroupData flag.

		int32 FilterLODLevel = -1;					// LOD level to filter out this mesh, value -1 disable this option. 

		// Precomputed meshes representing the instances geometry to varying quality levels (source LODs, simplified and approximated versions)
		TSharedPtr<FSinglePartMeshSet> PrecomputedMeshes;

		// in some cases it may be desirable to have "groups" of instances which should be output as separate meshes, but
		// be jointly processed in terms of (eg) the part LODs. If any InstanceSubsetID is non-zero, then instance subsets
		// are grouped/extracted by integer ID and will be returned as separate FOutputMesh's in the FResults. 
		// No hidden-removal, triangle merging optimization, etc will be performed between Instance Subsets.
		int32 InstanceSubsetID = 0;
	};


	/**
	 * FStaticMeshInstance represents a single instance of a static mesh asset
	 */
	struct FStaticMeshInstance : FBaseMeshInstance
	{
		UStaticMesh* SourceMesh = nullptr;
		UPrimitiveComponent* SourceComponent = nullptr;
		int32 SourceInstanceIndex = 0;		// custom index, eg if SourceComponent is an InstancedStaticMeshComponent, this is the Instance index
	};

	/**
	 * FMeshLODSet represents a list of LOD meshes that are used by one or more instances (eg like a StaticMesh, but could be other sources).
	 */
	struct FMeshLODSet
	{
		TArray<const FMeshDescription*> ReferencedMeshLODs;
		FKAggregateGeom SimpleCollisionShapes;
	};

	/**
	 * FMeshLODSetInstance represents a single instance of a FMeshLODSet
	 */
	struct FMeshLODSetInstance : FBaseMeshInstance
	{
		int32 MeshLODSetIndex = -1;		// index into a list of fixed Mesh LODs shared across multiple instances (eg FSourceInstanceList.MeshLODSets)

		int32 ExternalInstanceID = 0;	// external identifier used for Instance for debugging/convenience purposes, this is not used internally by CombineMeshInstances, 
	};

	/**
	 * FSourceInstanceList provides a flattened list of mesh instances to CombineMeshInstances.
	 * Each Instance can refer to shared data in the InstanceGroupDatas list
	 * 
	 * This data structure may be replaced in future w/ something more structured
	 */
	struct FSourceInstanceList
	{
		TArray<FStaticMeshInstance> StaticMeshInstances;
		TArray<FMeshLODSetInstance> MeshLODSetInstances;

		// mesh sets shared across instances
		TArray<FMeshLODSet> MeshLODSets;

		// sets of data shared across multiple instances
		TArray<FMeshInstanceGroupData> InstanceGroupDatas;
	};


	enum class ERemoveHiddenFacesMode
	{
		None = 0,
		Fastest = 1,

		ExteriorVisibility = 5,
		OcclusionBased = 6
	};


	enum class ECoarseApproximationStrategy
	{
		Automatic = 0,
		VoxelBasedSolidApproximation = 1,
		SweptPlanarProjection = 2,
		IntersectSweptPlanarProjections = 3
	};

	enum class EVertexColorMappingMode
	{
		None = 0,
		TriangleCountMetric = 1
	};

	struct FOptions
	{
		// number of requested LODs
		int32 NumLODs = 5;

		// settings for Copied LODs, these are directly copied from Source Geometry
		int32 BaseCopiedLOD = 0;
		int32 NumCopiedLODs = 1;

		//
		// Settings for Simplifed LODs
		//
		int32 NumSimplifiedLODs = 3;
		double SimplifyBaseTolerance = 1.0;
		double SimplifyLODLevelToleranceScale = 2.0;
		// if true, UVs will be preserved in Simplified LODs. This generally will result in lower-quality geometric shape approximation.
		bool bSimplifyPreserveUVs = false;
		// if true, vertex colors will be preserved in Simplified LODs. This generally will result in lower-quality geometric shape approximation.
		bool bSimplifyPreserveVertexColors = false;
		// if true, geometrically-detected "sharp" corners (eg like the corners of a box) will be preserved with hard constraints in Simplified LODs. This can be desirable on mechanical/geometric shapes.
		bool bSimplifyPreserveCorners = true;
		double SimplifySharpEdgeAngleDeg = 44.0;
		double SimplifyMinSalientDimension = 1.0;

		//
		// settings for Approximate LODs
		//
		
		// which LOD to use as basis for Approximations. Index is interpreted relative to [CopiedLODs...SimplifiedLODs] set, ie can be set to a Simplified LOD
		int32 ApproximationSourceLOD = 0;
		double OptimizeBaseTriCost = 0.7;
		double OptimizeLODLevelTriCostScale = 1.5;
		double MaxAllowableApproximationDeviation = 5.0;

		//
		// Settings for Coarse LODs. Coarse LODs are the lowest (furthest) LODs and generally assumed to have 
		// very low triangle counts. Two strategies are supported:
		//   1) VoxelBasedSolidApproximation - does a solid approximation with topological closure (outset + inset), which will fill in any small holes/gaps
		//      or small features. CoarseApproximationDetailSize is used as the closure radius/distance. 
		//   2) SweptPlanarProjection - The mesh is effectively projected to 2D along each X/Y/Z axis, with 2D polygon boolean & topological closure using CoarseApproximationDetailSize,
		//      as well as various other cleanup, and then that 2D polygon is triangulated and swept along the part bounding-box extent. The max distance
		//      is measured from the approximation to the original mesh, and the axis-swept-mesh with the smallest max-distance is used.
		// 
		// In either case, the resulting mesh is simplified to CoarseLODMaxTriCountBase, and then further simplified for each additional coarse LOD,
		// by halving the target triangle count. 
		// 
		// The 'Automatic' strategy uses the SweptPlanarProjection if it's max approximation deviation is within K*CoarseApproximationDetailSize (currently K=2),
		// and otherwise falls back to using VoxelBasedSolidApproximation
		//
		ECoarseApproximationStrategy CoarseLODStrategy = ECoarseApproximationStrategy::Automatic;
		int32 NumCoarseLODs = 1;
		double CoarseLODBaseTolerance = 1.0;
		int32 CoarseLODMaxTriCountBase = 500;
		double CoarseApproximationDetailSize = 10.0;

		//
		// Hidden Faces removal options
		// 
		
		// overall strategy to use for removing hidden faces
		ERemoveHiddenFacesMode RemoveHiddenFacesMethod = ERemoveHiddenFacesMode::None;
		// start removing hidden faces at this LOD level 
		int32 RemoveHiddenStartLOD = 0;
		// (approximately) spacing between samples on triangle faces used for determining exterior visibility
		double RemoveHiddenSamplingDensity = 1.0;
		// treat faces as double-sided for hidden removal
		bool bDoubleSidedHiddenRemoval = false;

		// LOD level to filter out detail parts
		int32 FilterDecorativePartsLODLevel = 2;
		// Decorative part will be approximated by simple shape for this many LOD levels before Filter level
		int ApproximateDecorativePartLODs = 1;

		// opening angle used to detect/assign sharp edges
		double HardNormalAngleDeg = 15.0;

		// UVs on input geometry will be preserved up to this LOD level (inclusive). 
		// Note that this setting will severely constrain and/or fully disable many other optimizations. 
		// In particular, Coplanar merging and retriangulation cannot be applied if UVs are to be preserved. 
		// WARNING: this LOD level must be <= (NumCopiedLODs+NumSimplifiedLODs)
		int PreserveUVLODLevel = -1;

		// Attempt to merge/weld coplanar areas after hidden removal, and then further simplify those merged areas
		// Coplanar merging is never applied between areas with different Materials. 
		// TriangleGroupingIDFunc below can be used to further demarcate important internal shape boundaries.
		bool bMergeCoplanarFaces = true;
		// LOD level at which coplanar face merging is applied. 
		int32 MergeCoplanarFacesStartLOD = 1;
		// TriangleGroupingIDFunc allows client to specify external adjacency relationships between triangles via an integer ID tuple.
		// Adjacent triangles will only be considered for Coplanar Merging if they have the same FIndex3i ID.
		TFunction<UE::Geometry::FIndex3i(const UE::Geometry::FDynamicMesh3& Mesh, int32 TriangleID)> TriangleGroupingIDFunc;

		// LOD level to attempt extraction and retriangulation of detected planar polygonal areas, after removing hidden faces.
		// This occurs after coplanar face merging, and may further merge adjacent coplanar faces.
		// Can significantly reduce triangle count, but attributes on the interiors of polygonal areas will be completely discarded
		int32 PlanarPolygonRetriangulationStartLOD = -1;

		// Triangles with Materials assigned that are also in this set will not be allowed to be combined/retriangulated
		// with adjacent triangles. This can be used to preserve topology/UVs/etc for specific material areas.
		TArray<UMaterialInterface*> PreventMergingMaterialSet;

		// If enabled, attempt to retriangulate planar areas of Source LODs to remove redundant coplanar geometry. 
		// This option affects individual parts and not the combined prefab.
		bool bRetriangulateSourceLODs = true;
		// which Source LOD to start planar retriangulation at
		int32 StartRetriangulateSourceLOD = 1;

		//
		// Optional Support for hitting explicit triangle counts for different LODs. 
		// The HardLODBudgets list should be provided in LOD order, ie first for Copied LODs,
		// then for Simplified LODs, then for Approximate LODs (Voxel LOD triangle counts are configured via VoxWrapMaxTriCountBase)
		// 
		// Currently the only explicit tri-count strategy in use is Part Promotion, where a coarser approximations
		// (eg Lower Copied LOD, Simplified LOD, Approximate LOD) are "promoted" upwards into higher combined-mesh LODs
		// as necessary to achieve the triangle count.
		// 
		// Note that final LOD triangle counts cannot be guaranteed, due to the combinatorial nature of the approximation.
		// For example the coarsest part LOD is a box with 12 triangles, so NumParts*12 is a lower bound on the initial combined mesh.
		// The Part Promotion strategy is applied *before* hidden removal and further mesh processing (eg coplanar merging),
		// so the final triangle count may be substantially lower than the budget (this is why the Multiplier is used below)
		//

		// list of fixed-triangle-count LOD budgets, in LOD order (ie LOD0, LOD1, LOD2, ...). If a triangle budgets are not specified for
		// a LOD, either by placing -1 in the array or truncating the array, that LOD will be left as-is. 
		TArray<int32> HardLODBudgets;
		// enable/disable the Part Promotion LOD strategy (described above)
		bool bEnableBudgetStrategy_PartLODPromotion = false;
		// Multiplier on LOD Budgets for PartLODPromotion strategy. This can be used to compensate for hidden-geometry removal and other optimizations done after the strategy is applied.
		double PartLODPromotionBudgetMultiplier = 2.0;



		//
		// Final processing options applied to each LOD after generation
		//

		// ensure that all triangles have some UV values set. Various geometry processing steps may discard UVs, this flag will
		// cause missing UVs to be recomputed (currently using a box projection)
		bool bAutoGenerateMissingUVs = false;

		// generate tangents on the output meshes - requires that UVs be available
		bool bAutoGenerateTangents = false;


		//
		// Debug/utility options
		// 

		// Color mapping modes for vertex colors, primarily used for debugging
		EVertexColorMappingMode VertexColorMappingMode = EVertexColorMappingMode::None;
	};

	enum class ETriangleBudgetMethod : uint8
	{
		/** Use each LOD as-is with no optimizations to the bricks themselves other than general purpose mesh optimizations. */
		NoRestriction,
		/** Try to fit the explicit budget. Note that success in hitting the budget depends on the MeshOptimizationMethod. */
		UseTriangleBudget,
		/** Try to fit a percentage of the prior budget. */
		UsePercentageOfPreviousLOD
	};

	enum class EMeshOptimizationMethod : uint8
	{
		/** No invasive part replacement to be used. Rely on the LODs defined in the bricks. */
		None,
		/** Go through the bricks and find the most expensive brick, try to approximate it using some geometry matching algorithms or decimation. Repeat until budget goal is met or all bricks have been handled. */
		SimplifyOrApproximate,
		/** Go through the bricks and find the most expensive brick, try to approximate it using some geometry matching algorithms. Repeat until budget goal is met or all bricks have been handled. */
		ApproximateOnly,
		/** Voxelize (or otherwise apply very coarse approximation) and decimate to hit budget goals. */
		VoxelizeAndDecimate
	};

	struct FTriangleBudgetOptions
	{
		/** Determine what the source for the budget should be. */
		ETriangleBudgetMethod Method = ETriangleBudgetMethod::NoRestriction;

		/** The explicit budget to use if Method == UseTriangleBudget */
		int32 TriangleBudget = 0;

		/** The percentage of the prior LOD to use as the triangle budget if Method == UsePercentageOfPreviousLOD */
		float LODReductionPercentage = 0.75f;
	};

	enum class EDecorationHandling : uint8
	{
		Keep,
		Approximate,
		Remove
	};

	// Options that can be set separately per LOD
	struct FCombineMeshInstancesOptionsPerLOD
	{
		/** What is the desired source mesh LOD levels to use for each brick. If not available, it will find the closest to this index. */
		int32 PreferredLOD = 0;

		/** The optimization method that can be used to generate this LOD */
		EMeshOptimizationMethod OptimizationMethod = EMeshOptimizationMethod::None;

		/** How decoration should be treated for this LOD (e.g., kept, approximated, or removed). */
		EDecorationHandling Decorations = EDecorationHandling::Keep;

		/** Tolerance to use for simplification of the LOD (if simplification is applied) */
		double SimplificationTolerance = 1.0;

		/** If two triangles are co-planar, determine if we can merge them  */
		bool bMergeCoplanarFaces = false;

		/** Whether UV coordinates must be preserved for this LOD, or can be fully discarded */
		bool bPreserveUVs = true;

		// Specifies whether to remove hidden faces, and if so method to use
		ERemoveHiddenFacesMode RemoveHiddenFacesMethod = ERemoveHiddenFacesMode::None;
		// (approximately) spacing between samples on triangle faces used for determining exterior visibility
		double RemoveHiddenSamplingDensity = 1.0;
		// treat faces as double-sided for hidden removal
		bool bDoubleSidedHiddenRemoval = false;

		// Whether to attempt extraction and retriangulation of detected planar polygonal areas, after removing hidden faces.
		// This occurs after coplanar face merging, and may further merge adjacent coplanar faces.
		// Can significantly reduce triangle count, but attributes on the interiors of polygonal areas will be completely discarded
		bool bPlanarPolygonRetriangulation = false;

		// Whether a part promotion strategy should be used to attempt to hit triangle budgets
		bool bEnableBudgetStrategy_PartLODPromotion = false;
		// If promoting parts to worse approximations to fit a triangle budget, scale the triangle budget by this amount (to account for potential reductions from e.g. hidden triangle removal, that will happen after part meshes are chosen and merged)
		double PartLODPromotionBudgetMultiplier = 2.0;

		// The budget settings for this LOD
		FTriangleBudgetOptions TriangleBudgetOptions;
	};

	// Options that apply across all LODs
	struct FCombineMeshInstancesOptionsGeneral
	{

		// TriangleGroupingIDFunc allows client to specify external adjacency relationships between triangles via an integer ID tuple.
		// Adjacent triangles will only be considered for Coplanar Merging if they have the same FIndex3i ID.
		TFunction<UE::Geometry::FIndex3i(const UE::Geometry::FDynamicMesh3& Mesh, int32 TriangleID)> TriangleGroupingIDFunc;

		// Triangles with Materials assigned that are also in this set will not be allowed to be combined/retriangulated
		// with adjacent triangles. This can be used to preserve topology/UVs/etc for specific material areas.
		TArray<UMaterialInterface*> PreventMergingMaterialSet;

		//
		// Common settings for Coarse/Voxel LODs. Coarse LODs should be the lowest-quality (highest-index) LODs and generally assumed to have 
		// very low triangle counts. Two strategies are supported:
		//   1) VoxelBasedSolidApproximation - does a solid approximation with topological closure (outset + inset), which will fill in any small holes/gaps
		//      or small features. CoarseApproximationDetailSize is used as the closure radius/distance. 
		//   2) SweptPlanarProjection - The mesh is effectively projected to 2D along each X/Y/Z axis, with 2D polygon boolean & topological closure using CoarseApproximationDetailSize,
		//      as well as various other cleanup, and then that 2D polygon is triangulated and swept along the part bounding-box extent. The max distance
		//      is measured from the approximation to the original mesh, and the axis-swept-mesh with the smallest max-distance is used.
		// 
		// The 'Automatic' strategy uses the SweptPlanarProjection if it's max approximation deviation is within K*CoarseApproximationDetailSize (currently K=2),
		// and otherwise falls back to using VoxelBasedSolidApproximation
		// 
		// Note the *first* coarse LOD will use the specified strategy, and subsequent LODs will apply mesh simplification to that result.
		// 
		ECoarseApproximationStrategy CoarseLODStrategy = ECoarseApproximationStrategy::Automatic;
		double CoarseLODBaseTolerance = 1.0;
		double CoarseApproximationDetailSize = 10.0;
		// opening angle used to detect/assign sharp edges
		double HardNormalAngleDeg = 15;

		//
		// Final processing options applied to each LOD after generation
		//

		// ensure that all triangles have some UV values set. Various geometry processing steps may discard UVs, this flag will
		// cause missing UVs to be recomputed (currently using a box projection)
		bool bAutoGenerateMissingUVs = false;

		// generate tangents on the output meshes - requires that UVs be available
		bool bAutoGenerateTangents = false;


		//
		// Debug/utility options
		// 

		// Color mapping modes for vertex colors, primarily used for debugging
		EVertexColorMappingMode VertexColorMappingMode = EVertexColorMappingMode::None;

		// Initialize options from the combined FOptions struct, including settings up per-LOD options
		void Init(const FOptions& InOptions, TArray<FCombineMeshInstancesOptionsPerLOD>& OutPerLODOptions)
		{
			// General

			TriangleGroupingIDFunc = InOptions.TriangleGroupingIDFunc;
			PreventMergingMaterialSet = InOptions.PreventMergingMaterialSet;
			CoarseLODStrategy = InOptions.CoarseLODStrategy;
			CoarseLODBaseTolerance = InOptions.CoarseLODBaseTolerance;
			CoarseApproximationDetailSize = InOptions.CoarseApproximationDetailSize;
			HardNormalAngleDeg = InOptions.HardNormalAngleDeg;
			bAutoGenerateMissingUVs = InOptions.bAutoGenerateMissingUVs;
			bAutoGenerateTangents = InOptions.bAutoGenerateTangents;
			VertexColorMappingMode = InOptions.VertexColorMappingMode;

			// Per LOD

			OutPerLODOptions.SetNum(InOptions.NumLODs);
			double SimplificationTolerance = InOptions.SimplifyBaseTolerance;
			for (int32 LODIndex = 0; LODIndex < InOptions.NumLODs; ++LODIndex)
			{
				FCombineMeshInstancesOptionsPerLOD& Options = OutPerLODOptions[LODIndex];
				Options.PreferredLOD = LODIndex;
				Options.OptimizationMethod = EMeshOptimizationMethod::SimplifyOrApproximate;
				if (LODIndex < InOptions.NumCopiedLODs)
				{
					Options.OptimizationMethod = EMeshOptimizationMethod::None;
				}
				else if (LODIndex < InOptions.NumCopiedLODs + InOptions.NumSimplifiedLODs)
				{
					Options.OptimizationMethod = EMeshOptimizationMethod::SimplifyOrApproximate;
				}
				else if (LODIndex >= InOptions.NumLODs - InOptions.NumCoarseLODs)
				{
					Options.OptimizationMethod = EMeshOptimizationMethod::VoxelizeAndDecimate;
				}
				Options.bDoubleSidedHiddenRemoval = InOptions.bDoubleSidedHiddenRemoval;
				Options.RemoveHiddenFacesMethod = InOptions.RemoveHiddenFacesMethod;
				Options.RemoveHiddenSamplingDensity = InOptions.RemoveHiddenSamplingDensity;
				Options.bMergeCoplanarFaces = InOptions.bMergeCoplanarFaces;
				Options.bPlanarPolygonRetriangulation = InOptions.bMergeCoplanarFaces && (InOptions.PlanarPolygonRetriangulationStartLOD > -1 && LODIndex >= InOptions.PlanarPolygonRetriangulationStartLOD);
				Options.Decorations = EDecorationHandling::Keep;
				if (LODIndex >= InOptions.FilterDecorativePartsLODLevel - InOptions.ApproximateDecorativePartLODs)
				{
					Options.Decorations = EDecorationHandling::Approximate;
				}
				else if (LODIndex >= InOptions.FilterDecorativePartsLODLevel)
				{
					Options.Decorations = EDecorationHandling::Remove;
				}
				Options.SimplificationTolerance = SimplificationTolerance;
				if (Options.OptimizationMethod != EMeshOptimizationMethod::None)
				{
					SimplificationTolerance *= InOptions.SimplifyLODLevelToleranceScale;
				}
				Options.bPreserveUVs = LODIndex <= InOptions.PreserveUVLODLevel;

				Options.bEnableBudgetStrategy_PartLODPromotion = InOptions.bEnableBudgetStrategy_PartLODPromotion;
				Options.PartLODPromotionBudgetMultiplier = InOptions.PartLODPromotionBudgetMultiplier;
				Options.TriangleBudgetOptions.Method = ETriangleBudgetMethod::NoRestriction;
				if (Options.OptimizationMethod != EMeshOptimizationMethod::VoxelizeAndDecimate)
				{
					if (InOptions.HardLODBudgets.IsValidIndex(LODIndex))
					{
						int32 HardBudget = InOptions.HardLODBudgets[LODIndex];
						if (HardBudget > -1)
						{
							Options.TriangleBudgetOptions.Method = ETriangleBudgetMethod::UseTriangleBudget;
							Options.TriangleBudgetOptions.TriangleBudget = HardBudget;
						}
					}
				}
				else
				{
					Options.TriangleBudgetOptions.Method = LODIndex > InOptions.NumLODs - InOptions.NumCoarseLODs ? ETriangleBudgetMethod::UsePercentageOfPreviousLOD : ETriangleBudgetMethod::UseTriangleBudget;
					Options.TriangleBudgetOptions.TriangleBudget = InOptions.CoarseLODMaxTriCountBase;
					Options.TriangleBudgetOptions.LODReductionPercentage = .5;
				}
			}
		}
	};


	struct FOutputMesh
	{
		TArray<UE::Geometry::FDynamicMesh3> MeshLODs;
		TArray<UMaterialInterface*> MaterialSet;

		FKAggregateGeom SimpleCollisionShapes;

		// Each part instance accumulated into the MeshLODs will have had this InstanceSubsetID in their input FBaseMeshInstance
		int32 InstanceSubsetID = 0;
	};


	struct FResults
	{
		//EResultCode ResultCode = EResultCode::UnknownError;

		TArray<FOutputMesh> CombinedMeshes;
	};




	virtual FOptions ConstructDefaultOptions()
	{
		check(false);		// not implemented in base class
		return FOptions();
	}


	virtual void CombineMeshInstances(
		const FSourceInstanceList& MeshInstances,
		const FOptions& Options,
		FResults& ResultsOut)
	{
		check(false);		// not implemented in base class
	}

	// General options for compute part meshes, which may be typically kept consistent across parts
	struct FComputePartMeshesOptions
	{
		//
		// Settings for Simplifed LODs
		//

		// which LOD to use as basis for Simplifications. Index is clamped to source LODs
		int32 SimplificationSourceLOD = 0;
		int32 NumSimplifiedLODs = 3;
		double SimplifyBaseTolerance = 1.0;
		double SimplifyLODLevelToleranceScale = 2.0;
		// if true, UVs will be preserved in Simplified LODs. This generally will result in lower-quality geometric shape approximation.
		bool bSimplifyPreserveUVs = false;
		// if true, vertex colors will be preserved in Simplified LODs. This generally will result in lower-quality geometric shape approximation.
		bool bSimplifyPreserveVertexColors = false;
		// if true, geometrically-detected "sharp" corners (eg like the corners of a box) will be preserved with hard constraints during simplification when creating Simplified LODs. This can be desirable on mechanical/geometric shapes.
		bool bSimplifyPreserveCorners = true;
		double SimplifySharpEdgeAngleDeg = 44.0;
		double SimplifyMinSalientDimension = 1.0;

		// Output mesh edges with larger dihedral angle than this threshold will be assigned sharp (split) normals
		double HardNormalAngleDeg = 15.0;

		//
		// settings for Approximate LODs
		//

		// which LOD to use as basis for Approximations. Index is interpreted relative to [CopiedLODs...SimplifiedLODs] set, ie can be set to a Simplified LOD
		int32 ApproximationSourceLOD = 0;
		double MaxAllowableApproximationDeviation = 5.0;
		// 'Triangle cost' to use for each approximation mesh; should be increasing.
		// The length of this array determines the number of approximations meshes to attempt to make. Note we may have fewer meshes, since we discard approximations that do not decrease triangle count.
		TArray<double> ApproximationTriCosts;

		// This helper configures ApproximationTriCosts, translating the automatic strategy used by the FOptions-based code to an explicit array of tri costs
		void SetApproximationLODs(int32 NumInitialApproximations, double OptimizeBaseTriCost, double InitialTriCostScale, int32 NumAdditionalApproximations = 10, double AdditionalTriCostAdd = 0.25)
		{
			int32 NumApproximatedLODs = NumInitialApproximations + NumAdditionalApproximations;
			ApproximationTriCosts.Reset(NumInitialApproximations + NumAdditionalApproximations);
			double TriCost = OptimizeBaseTriCost;
			ApproximationTriCosts.Add(TriCost);
			for (int32 Idx = 1; Idx < NumInitialApproximations; ++Idx)
			{
				TriCost *= OptimizeBaseTriCost;
				ApproximationTriCosts.Add(TriCost);
			}
			while (ApproximationTriCosts.Num() < NumApproximatedLODs)
			{
				TriCost += AdditionalTriCostAdd;
				ApproximationTriCosts.Add(TriCost);
			}
		}

		// If enabled, attempt to retriangulate planar areas of Source LODs to remove redundant coplanar geometry. 
		// This option affects individual parts and not the combined prefab.
		bool bRetriangulateSourceLODs = true;
		// which Source LOD to start planar retriangulation at
		int32 StartRetriangulateSourceLOD = 1;

		// UVs on input geometry will be preserved up to this LOD level (inclusive). 
		// Note that this setting will severely constrain and/or fully disable many other optimizations. 
		// In particular, Coplanar merging and retriangulation cannot be applied if UVs are to be preserved. 
		int PreserveUVLODLevel = -1;

		// Initialize part option settings from the larger FOptions struct
		void Init(const FOptions& Options)
		{
			SimplificationSourceLOD = Options.ApproximationSourceLOD;
			NumSimplifiedLODs = Options.NumSimplifiedLODs;
			SimplifyBaseTolerance = Options.SimplifyBaseTolerance;
			SimplifyLODLevelToleranceScale = Options.SimplifyLODLevelToleranceScale;
			bSimplifyPreserveUVs = Options.bSimplifyPreserveUVs;
			bSimplifyPreserveVertexColors = Options.bSimplifyPreserveVertexColors;
			bSimplifyPreserveCorners = Options.bSimplifyPreserveCorners;
			SimplifySharpEdgeAngleDeg = Options.SimplifySharpEdgeAngleDeg;
			SimplifyMinSalientDimension = Options.SimplifyMinSalientDimension;
			HardNormalAngleDeg = Options.HardNormalAngleDeg;
			ApproximationSourceLOD = Options.ApproximationSourceLOD;
			SetApproximationLODs(FMath::Max(0, Options.NumLODs - Options.NumCopiedLODs - Options.NumSimplifiedLODs - Options.NumCoarseLODs), Options.OptimizeBaseTriCost, Options.OptimizeLODLevelTriCostScale);
			MaxAllowableApproximationDeviation = Options.MaxAllowableApproximationDeviation;
			bRetriangulateSourceLODs = Options.bRetriangulateSourceLODs;
			StartRetriangulateSourceLOD = Options.StartRetriangulateSourceLOD;
			PreserveUVLODLevel = Options.PreserveUVLODLevel;
		}
	};

	// Options that are specific to a given part / more likely to be changed per-part
	struct FComputePartMeshesSinglePartOptions
	{
		FComputePartMeshesSinglePartOptions() = default;
		FComputePartMeshesSinglePartOptions(EApproximationType ApproximationConstraint, bool bPreserveUVs = false) : ApproximationConstraint(ApproximationConstraint), bPreserveUVs(bPreserveUVs) {}

		// Whether to constrain this part to only use specific approximation methods
		EApproximationType ApproximationConstraint = EApproximationType::NoConstraint;

		// Whether to always preserve UVs for this part. If false, UVs may still be preserved based on the general settings
		bool bPreserveUVs = false;
	};

	// Generate a set of coarse simplification and/or approximation LODs from a source array of mesh descriptions per LOD
	// @param SourceMeshLODs Source mesh LODs as mesh descriptions
	// @param GeneralOptions Sets the number and settings for simplified and approximated meshes
	// @param PartOptions Options more likely to vary on a per-part basis
	// @param ResultMeshes Dynamic meshes of the source LODs as well as simplified and approximated versions
	virtual void ComputeSinglePartMeshSet(
		TConstArrayView<const FMeshDescription*> SourceMeshLODs,
		const FComputePartMeshesOptions& GeneralOptions,
		const FComputePartMeshesSinglePartOptions& PartOptions, 
		FSinglePartMeshSet& ResultMeshes
	)
	{
		check(false); // not implemented in base class
	}

	// Generate a set of coarse simplification and/or approximation LODs from a source static mesh
	// @param SourceMeshLODs Source mesh LODs as mesh descriptions
	// @param GeneralOptions Sets the number and settings for simplified and approximated meshes
	// @param PartOptions Options more likely to vary on a per-part basis
	// @param ResultMeshes Dynamic meshes of the source LODs as well as simplified and approximated versions
	virtual void ComputeSinglePartMeshSet(
		UStaticMesh* SourceMesh,
		const FComputePartMeshesOptions& GeneralOptions,
		const FComputePartMeshesSinglePartOptions& PartOptions,
		FSinglePartMeshSet& ResultPartMeshSet
	)
	{
		check(false); // not implemented in base class
	}

	// Generate a set of coarse simplification and/or approximation LODs for all source meshes in the given MeshInstances
	// @param MeshInstances Instances to generate part meshes for. Shared pointers to computed part meshes will also be assigned per instance
	// @param Options Sets the number and settings for simplified and approximated meshes
	// @param bKeepExistingPartMeshes If false will clear the existing part mesh cache, otherwise will only generate part meshes for those instance that do not already have them.
	// @param ResultMeshSets Computed mesh sets
	virtual void ComputePartMeshSets(
		FSourceInstanceList& MeshInstances,
		const FComputePartMeshesOptions& Options,
		bool bKeepExistingPartMeshes,
		TArray<TSharedPtr<FSinglePartMeshSet>>& ResultMeshSets
	)
	{
		check(false); // not implemented in base class
	}

	// A version of CombineMeshInstances that takes per-LOD settings and uses pre-computed part meshes (computed via ComputePartMeshSets, above)
	virtual void CombineMeshInstances(
		const FSourceInstanceList& MeshInstances,
		const FCombineMeshInstancesOptionsGeneral& AllLODOptions,
		TConstArrayView<FCombineMeshInstancesOptionsPerLOD> PerLODOptions,
		FResults& ResultsOut
	)
	{
		check(false);		// not implemented in base class
	}


	// Modular feature name to register for retrieval during runtime
	static const FName GetModularFeatureName()
	{
		return TEXT("GeometryProcessing_CombineMeshInstances");
	}

};